{"data-analyze": {"category": {"gremlin-analyze": "Gremlin 分析", "algorithm-analyze": "算法分析"}, "manipulations": {"execution": "执行", "favorite": "收藏", "reset": "重置"}, "hint": {"graph-disabled": "该图不可用"}, "algorithm-list": {"title": "算法目录", "loop-detection": "环路检测", "focus-detection": "交点检测", "shortest-path": "最短路径", "shortest-path-all": "全最短路径", "all-path": "所有路径", "model-similarity": "模型相似度算法", "neighbor-rank": "Neighbor Rank推荐算法", "k-step-neighbor": "k步邻居", "k-hop": "k跳算法", "custom-path": "自定义路径", "radiographic-inspection": "射线检测", "same-neighbor": "共同邻居", "weighted-shortest-path": "带权最短路径", "single-source-weighted-shortest-path": "单源带权最短路径", "jaccard": "<PERSON><PERSON><PERSON>相似度", "personal-rank": "Personal Rank推荐算法"}, "algorithm-forms": {"loop-detection": {"options": {"source": "起点ID:", "direction": "方向:", "max_depth": "最大步数:", "label": "边类型:", "max_degree": "最大度数:", "source_in_ring": "环路包含起点:", "limit": "返回可达路径最大值:", "capacity": "访问顶点最大值:"}, "pre-value": "全部", "placeholder": {"input-source-id": "请输入起点ID", "input-positive-integer-or-negative-one-max-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-capacity": "请填写-1或大于0的整数，默认为10000000", "input-positive-integer-or-negative-one-limit": "请填写-1或大于0的整数，默认为10", "input-positive-integer": "请填写大于0的整数", "no-edge-types": "无边类型"}, "hint": {"max-depth": "为保证性能，建议不超过10步，推荐5步", "max-degree": "查询过程中，单个顶点的最大边数目"}, "validations": {"no-empty": "该项不能为空", "postive-integer-only": "请填写大于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数"}}, "focus-detection": {"options": {"source": "起点ID:", "target": "终点ID:", "direction": "方向:", "max_depth": "最大步数:", "label": "边类型:", "max_degree": "最大度数:", "capacity": "访问顶点最大值:", "limit": "返回交点最大值:"}, "pre-value": "全部", "placeholder": {"input-source-id": "请输入起点ID", "input-target-id": "请输入终点ID", "input-positive-integer-or-negative-one-max-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-capacity": "请填写-1或大于0的整数，默认为10000000", "input-positive-integer-or-negative-one-limit": "请填写-1或大于0的整数，默认为10", "input-positive-integer": "请填写大于0的整数", "no-edge-types": "无边类型"}, "hint": {"max-depth": "为保证性能，建议不超过10步，推荐5步", "max-degree": "查询过程中，单个顶点的最大边数目"}, "validations": {"no-empty": "该项不能为空", "postive-integer-only": "请填写大于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数"}}, "shortest-path": {"options": {"source": "起点ID:", "target": "终点ID:", "direction": "方向:", "max_depth": "最大步数:", "label": "边类型:", "max_degree": "最大度数:", "skip_degree": "超级顶点度数:", "capacity": "访问顶点最大值:"}, "pre-value": "全部", "placeholder": {"input-source-id": "请输入起点ID", "input-target-id": "请输入终点ID", "input-positive-integer-or-negative-one-max-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-capacity": "请填写-1或大于0的整数，默认为10000000", "input-integer": "请填写大于等于0的整数，默认为0", "input-positive-integer": "请填写大于0的整数", "no-edge-types": "无边类型"}, "hint": {"max-depth": "为保证性能，建议不超过10步，推荐5步", "max-degree": "查询过程中，单个顶点的最大边数目", "skip-degree": "填写查询过程中需要跳过的顶点的最小的边数目，即当顶点的边数目大于超级顶点度数时，跳过该顶点，可用于规避超级点"}, "validations": {"no-empty": "该项不能为空", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数", "integer-only": "请填写大于等于0的整数", "postive-integer-only": "请填写大于0的整数"}}, "shortest-path-all": {"options": {"source": "起点ID:", "target": "终点ID:", "direction": "方向:", "max_depth": "最大步数:", "label": "边类型:", "max_degree": "最大度数:", "capacity": "访问顶点最大值:", "skip_degree": "超级顶点度数:"}, "pre-value": "全部", "placeholder": {"input-source-id": "请输入起点ID", "input-target-id": "请输入终点ID", "input-positive-integer-or-negative-one-max-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-capacity": "请填写-1或大于0的整数，默认为10000000", "input-positive-integer": "请填写大于0的整数", "input-integer": "请填写大于等于0的整数，默认为0", "no-edge-types": "无边类型"}, "hint": {"max-depth": "为保证性能，建议不超过10步，推荐5步", "max-degree": "查询过程中，单个顶点的最大边数目", "skip-degree": "填写查询过程中需要跳过的顶点的最小的边数目，即当顶点的边数目大于超级顶点度数时，跳过该顶点，可用于规避超级点"}, "validations": {"no-empty": "该项不能为空", "integer-only": "请填写大于等于0的整数", "postive-integer-only": "请填写大于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数"}}, "all-path": {"options": {"source": "起点ID:", "target": "终点ID:", "direction": "方向:", "max_depth": "最大步数:", "label": "边类型:", "max_degree": "最大度数:", "capacity": "访问顶点最大值:", "limit": "返回路径最大值:"}, "pre-value": "全部", "placeholder": {"input-source-id": "请输入起点ID", "input-target-id": "请输入终点ID", "input-positive-integer-or-negative-one-max-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-capacity": "请填写-1或大于0的整数，默认为10000000", "input-positive-integer-or-negative-one-limit": "请填写-1或大于0的整数，默认为10", "input-positive-integer": "请填写大于0的整数", "no-edge-types": "无边类型"}, "hint": {"max-depth": "为保证性能，建议不超过10步，推荐5步", "max-degree": "查询过程中，单个顶点的最大边数目"}, "validations": {"no-empty": "该项不能为空", "postive-integer-only": "请填写大于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数"}}, "model-similarity": {"options": {"method": "起点选择方式:", "source": "起点ID:", "vertex-type": "顶点类型:", "vertex-property": "顶点属性及值:", "direction": "方向:", "least_neighbor": "最少邻居数:", "similarity": "相似度:", "label": "边类型:", "max_similar": "相似度最高个数:", "least_similar": "模形相似点最小个数:", "property_filter": "属性过滤:", "least_property_number": "最小属性值个数:", "max_degree": "最大度数:", "capacity": "访问顶点最大值:", "skip_degree": "返回顶点最大值:", "limit": "返回结果最大值:", "return_common_connection": "返回共同关联点:", "return_complete_info": "返回顶点完整信息:"}, "radio-value": {"specific-id": "指定ID", "filtered-type-property": "筛选类型属性"}, "placeholder": {"input-source-id": "请输入起点ID，多个ID用逗号分隔", "input-vertex-type": "请选择顶点类型", "select-vertex-property": "请选择顶点属性", "input-vertex-property": "多属性值以逗号分隔", "input-positive-integer-or-negative-one-max-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-capacity": "请填写-1或大于0的整数，默认为10000000", "input-positive-integer-or-negative-one-limit": "请填写-1或大于0的整数，默认为10", "input-integer": "请填写大于等于0的整数", "input-positive-integer": "请填写大于0的整数", "input-integer-gt-1": "请填写大于1的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数", "input-filtered-property": "请选择需要过滤的属性", "no-properties": "无属性", "no-vertex-type": "无顶点类型", "similarity": "请输入(0-1]的数字"}, "hint": {"vertex_type_or_property": "顶点类型/顶点属性至少填写一项", "least_property_number": "属性过滤和最小属性值个数需一起使用；设置后效果为：当起点跟其所有的梭形相似点某个属性的值大于等于最小属性值个数时，才会返回该起点及其梭形相似点", "max-degree": "查询过程中，单个顶点的最大边数目", "least_neighbor": "邻居数少于当前设定值，则认为起点没有梭形相似点", "similarity": "起点与\"梭形相似点\"的共同邻居数目占起点的全部邻居数目的比例", "max_similar": "返回起点的梭形相似点中相似度最高的top个数，0表示全部", "return_common_connection": "是否返回起点及其\"梭形相似点\"共同关联的中间点"}, "validations": {"no-empty": "该项不能为空", "no-edge-typs": "无边类型", "integer-only": "请填写大于等于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数", "postive-integer-only": "请填写大于0的整数", "integer-gt-1": "请填写大于1的整数", "similarity": "请输入(0-1]的数字", "no-gt-1000": "该值不能大于等于1000"}, "add": "添加", "delete": "删除", "pre-value": "全部"}, "neighbor-rank": {"options": {"source": "起点ID:", "alpha": "Alpha:", "direction": "方向:", "capacity": "访问顶点最大值:", "label": "边类型:", "degree": "最大度数:", "top": "每层保留权重Top N:", "steps": "steps:"}, "placeholder": {"input-source-id": "请输入起点ID", "input-integer-lt-1000": "请填写大于等于0小于1000的整数, 默认为100", "input-positive-integer-or-negative-one-max-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-capacity": "请填写-1或大于0的整数，默认为10000000", "input-positive-integer": "请填写大于0的整数", "range": "范围(0-1]"}, "hint": {"top": "在结果中每一层只保留权重最高的N个结果", "max-degree": "查询过程中，单个顶点的最大边数目"}, "validations": {"no-empty": "该项不能为空", "no-edge-typs": "无边类型", "range": "请填写大于0且小于等于1的数值", "integer-only-lt-1000": "请填写大于等于0的整数小于1000的整数", "postive-integer-only": "请填写大于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数"}, "pre-value": "全部", "add-new-rule": "添加规则"}, "k-step-neighbor": {"options": {"source": "起点ID:", "direction": "方向:", "max_depth": "最大步数:", "label": "边类型:", "max_degree": "最大度数:", "limit": "返回顶点最大值:"}, "pre-value": "全部", "placeholder": {"input-source-id": "请输入起点ID", "input-positive-integer-or-negative-one-max-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-limit": "请填写-1或大于0的整数，默认为10", "input-positive-integer": "请填写大于0的整数", "no-edge-types": "无边类型"}, "hint": {"max-depth": "为保证性能，建议不超过10步，推荐5步", "max-degree": "查询过程中，单个顶点的最大边数目"}, "validations": {"no-empty": "该项不能为空", "postive-integer-only": "请填写大于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数"}}, "k-hop": {"options": {"source": "起点ID:", "direction": "方向:", "max_depth": "最大步数:", "nearest": "最短路径:", "label": "边类型:", "max_degree": "最大度数:", "capacity": "遍历中访问顶点最大值:", "limit": "返回顶点最大值:"}, "pre-value": "全部", "placeholder": {"input-source-id": "请输入起点ID", "input-positive-integer-or-negative-one-max-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-capacity": "请填写-1或大于0的整数，默认为10000000", "input-positive-integer-or-negative-one-limit": "请填写-1或大于0的整数，默认为10", "input-positive-integer": "请填写大于0的整数", "no-edge-types": "无边类型"}, "hint": {"max-depth": "为保证性能，建议不超过10步，推荐5步", "max-degree": "查询过程中，单个顶点的最大边数目", "shortest-path": "开启后，则查询出起始顶点最短路径为depth步的顶点，关闭后，则查询出起始顶点路径为depth步的顶点，可能有环，且不一定是最短路径"}, "validations": {"no-empty": "该项不能为空", "postive-integer-only": "请填写大于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数"}}, "custom-path": {"options": {"method": "起点选择方式:", "source": "起点ID:", "vertex-type": "顶点类型:", "vertex-property": "顶点属性:", "sort_by": "路径权重排序:", "capacity": "遍历中访问顶点最大值:", "limit": "返回顶点最大值:", "direction": "方向:", "labels": "边类型:", "properties": "边属性:", "weight_by": "根据属性计算边权重:", "degree": "最大度数:", "sample": "采样值:", "steps": "steps:"}, "placeholder": {"input-source-id": "请输入起点ID，多个ID用逗号分隔", "select-vertex-type": "请选择顶点类型", "select-vertex-property": "请选择顶点属性", "input-multiple-properties": "多属性值以逗号分隔", "input-integer": "请填写大于等于0的整数", "input-positive-integer": "请填写大于0的整数", "input-positive-integer-or-negative-one-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-capacity": "请填写-1或大于0的整数，默认为10000000", "input-positive-integer-or-negative-one-limit": "请填写-1或大于0的整数，默认为10", "input-property": "请输入边属性", "input-number": "请输入浮点数字", "select-edge-type": "请选择边类型", "select-edge-property": "请选择边属性", "select-property": "请选择属性", "no-vertex-type": "无顶点类型", "no-vertex-property": "无顶点属性", "no-edge-type": "无边类型", "no-edge-property": "无边属性", "no-properties": "无属性"}, "hint": {"top": "在结果中每一层只保留权重最高的N个结果", "vertex_type_or_property": "顶点类型/顶点属性至少填写一项"}, "radio-value": {"specific-id": "指定ID", "filtered-type-property": "筛选类型属性", "none": "不排序", "ascend": "升序", "descend": "降序"}, "validations": {"no-empty": "该项不能为空", "no-edge-typs": "无边类型", "range": "请填写大于0且小于等于1的数值", "integer-only": "请填写大于等于0的整数", "postive-integer-only": "请填写大于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数", "input-number": "请输入浮点数字"}, "custom-weight": "自定义权重值", "add": "添加", "delete": "删除", "add-new-rule": "添加规则"}, "radiographic-inspection": {"options": {"source": "起点ID:", "direction": "方向:", "max_depth": "最大步数:", "label": "边类型:", "max_degree": "最大度数:", "capacity": "遍历中访问顶点最大值:", "limit": "返回非环路路径最大值:"}, "pre-value": "全部", "placeholder": {"input-source-id": "请输入起点ID", "input-positive-integer": "请填写大于0的整数", "input-positive-integer-or-negative-one-max-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-capacity": "请填写-1或大于0的整数，默认为10000000", "input-positive-integer-or-negative-one-limit": "请填写-1或大于0的整数，默认为10000000", "no-edge-types": "无边类型"}, "hint": {"max-depth": "为保证性能，建议不超过10步，推荐5步", "max-degree": "查询过程中，单个顶点的最大边数目"}, "validations": {"no-empty": "该项不能为空", "postive-integer-only": "请填写大于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数"}}, "same-neighbor": {"options": {"vertex": "顶点1:", "other": "顶点2:", "direction": "方向:", "label": "边类型:", "max_degree": "最大度数:", "limit": "返回共同邻居最大值:"}, "pre-value": "全部", "placeholder": {"input-source-id": "请输入顶点ID", "input-other-id": "请输入不同于顶点1的ID", "input-positive-integer-or-negative-one-max-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-limit": "请填写-1或大于0的整数，默认为10000000", "input-positive-integer": "请填写大于0的整数", "no-edge-types": "无边类型"}, "hint": {"max-depth": "为保证性能，建议不超过10步，推荐5步", "max-degree": "查询过程中，单个顶点的最大边数目"}, "validations": {"no-empty": "该项不能为空", "postive-integer-only": "请填写大于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数", "no-same-value-with-other": "不能与顶点2相同", "no-same-value-with-vertex": "不能与顶点1相同"}}, "weighted-shortest-path": {"options": {"source": "起点ID:", "target": "终点ID:", "direction": "方向:", "weight": "权重属性:", "with_vertex": "返回顶点完整信息:", "label": "边类型:", "max_degree": "最大度数:", "skip_degree": "跳过点的度数:", "capacity": "遍历中访问顶点最大值:"}, "pre-value": "全部", "placeholder": {"input-source-id": "请输入起点ID", "input-target-id": "请输入终点ID", "input-integer": "请填写大于等于0的整数", "input-positive-integer-or-negative-one-max-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-capacity": "请填写-1或大于0的整数，默认为10000000", "select-property": "请选择属性", "input-positive-integer": "请填写大于0的整数，默认为0", "no-property": "无属性值为数字类型的属性", "no-edge-types": "无边类型"}, "hint": {"max-depth": "为保证性能，建议不超过10步，推荐5步", "skip-degree": "当顶点的边数目大于填写值，则跳过当前顶点，用于规避超级点", "max-degree": "查询过程中，单个顶点的最大边数目"}, "validations": {"no-empty": "该项不能为空", "integer-only": "请填写大于等于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数", "postive-integer-only": "请填写大于0的整数"}}, "single-source-weighted-shortest-path": {"options": {"source": "起点ID:", "direction": "方向:", "weight": "权重属性:", "with_vertex": "返回顶点完整信息:", "label": "边类型:", "max_degree": "最大度数:", "skip_degree": "跳过点的度数:", "capacity": "遍历中访问顶点最大值:", "limit": "返回顶点/最短路径最大值:"}, "pre-value": "全部", "placeholder": {"input-source-id": "请输入起点ID", "input-integer": "请填写大于等于0的整数，默认为0", "input-positive-integer": "请填写大于0的整数", "input-positive-integer-or-negative-one-max-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-capacity": "请填写-1或大于0的整数，默认为10000000", "input-positive-integer-or-negative-one-limit": "请填写-1或大于0的整数，默认为10", "no-property": "若不填写权重为1.0", "no-edge-types": "无边类型"}, "hint": {"max-depth": "为保证性能，建议不超过10步，推荐5步", "skip-degree": "当顶点的边数目大于填写值，则跳过当前顶点，用于规避超级点", "max-degree": "查询过程中，单个顶点的最大边数目"}, "validations": {"no-empty": "该项不能为空", "integer-only": "请填写大于等于0的整数", "postive-integer-only": "请填写大于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数"}}, "jaccard": {"options": {"vertex": "顶点1:", "other": "顶点2:", "direction": "方向:", "label": "边类型:", "max_degree": "最大度数:"}, "pre-value": "全部", "placeholder": {"input-source-id": "请输入顶点ID", "input-other-id": "请输入不同于顶点1的ID", "input-positive-integer": "请填写大于0的整数", "input-positive-integer-or-negative-one-max-degree": "请填写-1或大于0的整数，默认为10000", "no-edge-types": "无边类型"}, "hint": {"max-depth": "为保证性能，建议不超过10步，推荐5步", "max-degree": "查询过程中，单个顶点的最大边数目"}, "validations": {"no-empty": "该项不能为空", "postive-integer-only": "请填写大于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数", "no-same-value-with-other": "不能与顶点2相同", "no-same-value-with-vertex": "不能与顶点1相同"}}, "personal-rank": {"options": {"source": "起点ID:", "alpha": "Alpha:", "max_depth": "迭代次数:", "with_label": "返回结果筛选:", "label": "边类型:", "degree": "最大度数:", "limit": "返回顶点最大值:", "sorted": "返回结果排序:"}, "with-label-radio-value": {"same_label": "相同类型顶点", "other_label": "不同类型顶点", "both_label": "全部类型顶点"}, "placeholder": {"input-source-id": "请输入起点ID", "input-positive-integer-or-negative-one-degree": "请填写-1或大于0的整数，默认为10000", "input-positive-integer-or-negative-one-limit": "请填写-1或大于0的整数，默认为10000000", "select-edge": "请选择边类型", "input-positive-integer": "请填写大于0的整数", "alpha": "请输入(0-1]的数字", "max_depth": "请输入(0-50]的数字"}, "hint": {"degree": "查询过程中，单个顶点的最大边数目", "with-label": "根据是否与起点类型相同，筛选返回结果", "sorted": "选择开，则降序排列，选择关，则不排序"}, "validations": {"no-empty": "该项不能为空", "no-edge-typs": "无边类型", "alpha-range": "请填写大于0且小于等于1的数值", "depth-range": "请填写大于0且小于等于50的数值", "postive-integer-only": "请填写大于0的整数", "positive-integer-or-negative-one-only": "请填写-1或大于0的整数"}, "pre-value": "全部", "add-new-rule": "添加规则"}, "api-name-mapping": {"rings": "环路检测", "crosspoints": "交点检测", "shortpath": "最短路径", "allshortpath": "全最短路径", "paths": "所有路径", "fsimilarity": "模型相似度算法", "neighborrank": "Neighbor Rank推荐算法", "kneighbor": "k步邻居", "kout": "k跳算法", "customizedpaths": "自定义路径", "rays": "射线检测", "sameneighbors": "共同邻居", "weightedshortpath": "带权最短路径", "singleshortpath": "单源带权最短路径", "jaccardsimilarity": "<PERSON><PERSON><PERSON>相似度", "personalrank": "Personal Rank推荐算法"}}, "exec-logs": {"table-title": {"time": "时间", "type": "执行类型", "content": "执行内容", "status": "状态", "duration": "耗时", "manipulation": "操作"}, "type": {"GREMLIN": "GREMLIN 查询", "GREMLIN_ASYNC": "GREMLIN 任务", "ALGORITHM": "算法查询"}, "status": {"success": "成功", "async-success": "提交成功", "running": "运行中", "failed": "失败", "async-failed": "提交失败"}}}}