/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with this
 * work for additional information regarding copyright ownership. The ASF
 * licenses this file to You under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

const {
  override,
  addLessLoader,
  addWebpackAlias,
  overrideDevServer,
  watchAll,
  addWebpackPlugin
} = require('customize-cra');
const { ProgressPlugin } = require("webpack")
const addProxy = () => (configFunction) => {
  configFunction.proxy = {
    '/about': {
      target: 'http://127.0.0.1:8088',
      changeOrigin: true
    },
    '/api': {
      target: 'http://127.0.0.1:8088',
      changeOrigin: true
    }
  };

  return configFunction;
};

module.exports = {
  webpack: override(
    addLessLoader({
      javascriptEnabled: true
    }),
    addWebpackAlias({
      'hubble-ui': require('path').resolve(__dirname, './src/components/hubble-ui')
    }),
    addWebpackPlugin(new ProgressPlugin()),
  ),
  devServer: overrideDevServer(addProxy(), watchAll())
};
