{"breadcrumb": {"first": "import task", "second": "Task Details"}, "import-manager": {"hint": {"empty-task": "you have no tasks yet, create one now", "no-result": "no results", "creation-succeed": "Creation Succeeded", "update-succeed": "Edit Succeeded"}, "manipulation": {"create": "create"}, "placeholder": {"input-job-name": "Please Enter Task so to search", "input-valid-job-name": "Allow is Chinese, English, Numbers, Underscores", "input-job-description": "Please Enter Task Description"}, "list-column-title": {"job-name": "task name", "size": "size", "create-time": "creation time", "status": "status", "time-consuming": "time consumed", "manipulation": "operation"}, "list-column-status": {"DEFAULT": "not started", "UPLOADING": "not started", "MAPPING": "setting", "SETTING": "Importing", "LOADING": "Importing", "FAILED": "failed", "SUCCESS": "succeeded"}, "list-column-manipulations": {"start": "Start Task", "resume-setting": "constinue setting", "resume-importing": "continue improTing", "check-error-log": "check rea<PERSON>on", "delete": "delete"}, "modal": {"create-job": {"title": "Create Import Task", "job-name": "task name:", "job-description": "task description:"}, "delete-job": {"title": "delete task", "hint": "Confirm delete task {{So}}?", "sub-hint": "This task cannot be recovered after deletion"}, "manipulations": {"create": "create", "delete": "delete", "cancel": "cancel"}}, "validator": {"no-empty": "this fire is required", "over-limit-size": "Exceeed Character Limit", "invalid-format": "Please Enter Chinese, English, Numbers, Underscores"}}, "import-job-details": {"tabs": {"basic-settings": "Basic Settings", "uploaded-files": "uploaded files", "data-maps": "MAPPING FILES", "import-details": "Import Details"}, "basic": {"job-name": "task name:", "job-description": "task description:", "modal": {"edit-job": {"title": "Edit Import Task", "job-name": "task name:", "job-description": "task description:"}, "manipulations": {"save": "save", "cancel": "cancel"}}}, "manipulations": {"edit": "edit", "resume-task": "resume task"}}, "step": {"first": "upload files", "second": "set data mapping", "third": "import data", "fourth": "complete"}, "upload-files": {"click": "CLICK", "description": "Click or Drag Files Hereto Upload, Multiple CSV Files Supported, Single File Up TO 1GB, Total up to 10GB", "drag": "Drag", "description-1": "OR DRAG FILES", "description-2": "Here to upload, multiple CSV Files SUPPORTED, Single File up to 1GB, Total up to 10GBBGB", "cancel": "Cancel Upload", "next": "next", "wrong-format": "ONLY CSV Format Files Are Supported", "over-single-size-limit": "SIZE Exceeds 1 GB", "over-all-size-limit": "Total Size Exceeds 10 GB", "empty-file": "file is empty, please re-upload", "no-duplicate": "the footowing uploaded files already exist:"}, "data-configs": {"file": {"title": "file settings", "include-header": "Include Header", "delimiter": {"title": "delimiter", "comma": "comma", "semicolon": "semicolon", "tab": "tab", "space": "space", "custom": "custom"}, "code-type": {"title": "encoding format", "UTF-8": "UTF-8", "GBK": "GBK", "ISO-8859-1": "ISO-8859-1", "US-ASCII": "US-Ascii", "custom": "custom"}, "date-type": {"title": "date format", "custom": "custom"}, "skipped-line": "skipped line", "timezone": "Timezone", "save": "save", "placeholder": {"input-delimiter": "Please Enter Delimiter", "input-charset": "Please Enter Encoding Format", "input-date-format": "Please Enter date format"}, "hint": {"save-succeed": "file settings saved"}}, "type": {"title": "TypeSettings", "basic-settings": "Basic Settings", "manipulation": {"create": "create", "save": "save", "cancel": "cancel", "create-vertex": "create vertex mapping", "create-edge": "Create EDGE MAPPING"}, "info": {"type": "Type", "name": "name", "ID-strategy": "Id strategy", "edit": "edit", "delete": "delete"}, "ID-strategy": {"PRIMARY_KEY": "Primary Key ID", "AUTOMATIC": "Automaticly Generated", "CUSTOMIZE_STRING": "CUSTOM String", "CUSTOMIZE_NUMBER": "CUSTOM NUMBER", "CUSTOMIZE_UUID": "CUSTOM UUID"}, "vertex": {"title": "create vertex mapping", "type": "vertex type", "ID-strategy": "Id strategy", "ID-column": "Id column", "map-settings": "MAPPING SETTINGS", "add-map": {"title": "add maping", "name": "column name", "sample": "Data Sample", "property": "MAPPING PROPERTY", "clear": "clear"}, "select-all": "Select all", "advance": {"title": "Advanced Settings", "nullable-list": {"title": "null able list", "empty": "Empty Value", "custom": "custom"}, "map-property-value": {"title": "Property Value Mapping", "add-value": "Add Property Value Mapping", "fields": {"property": "Property", "value-map": "Value Map", "add-value-map": "add value map"}}, "placeholder": {"input": "Please Select", "input-property": "Please Select input Property", "input-file-value": "Please Enter File Value", "input-graph-value": "Please Enter Graph Import Value"}}}, "edge": {"title": "Create EDGE MAPPING", "type": "EDGE TYPE", "source-ID-strategy": "source ID Strategy", "target-ID-strategy": "target ID Strategy", "ID-column": "Id column", "map-settings": "MAPPING SETTINGS", "add-map": {"title": "add maping", "name": "column name", "sample": "Data Sample", "property": "MAPPING PROPERTY", "clear": "clear"}, "select-all": "Select all", "advance": {"title": "Advanced Settings", "nullable-list": {"title": "null able list", "empty": "Empty Value", "custom": "custom"}, "map-property-value": {"title": "Property Value Mapping", "add-value": "Add Property Value Mapping", "fields": {"property": "Property", "value-map": "Value Map", "add-value-map": "add value map"}}, "placeholder": {"input": "Please Select Mapping Property", "input-property": "Please Select input Property", "input-file-value": "Please Enter File Value", "input-graph-value": "Please Enter Graph Import Value"}}}, "hint": {"lack-support-for-automatic": "Automaticly Generated ID Strategy Does Not Support Visual Import, Please Call API", "no-vertex-or-edge-mapping": "The Follow Files have not set verstex or edge type mapping:"}, "placeholder": {"select-vertex-type": "Please Select vertex Type", "select-edge-type": "Please selectd a Type", "select-id-column": "Please Select ID Column", "empty-value": "empty"}}, "manipulations": {"previous": "previous", "next": "next", "add": "ADD", "edit": "edit", "delete": "delete", "cancel": "cancel", "hints": {"delete-confirm": "Confirm delete?", "warning": "Cannot be recovered after deletion, please openly carefully"}}, "validator": {"no-empty": "this fire cannot be empty"}}, "server-data-import": {"import-settings": {"title": "import settings", "checkIfExist": "Check if version connected by edest", "requestTimesWhenInterpolationFailed": "Retry Times When Insertion Fails", "maximumAnalyzedErrorRow": "maximum Allowed Parsing Error ROWS", "requestTicksWhenInterpolationFailed": "Retry Interval for Insertion Failures/Yes", "maxiumInterpolateErrorRow": "maximum Allowed Insertion Error ROWS", "InterpolationTimeout": "INSERTION TIMEOUT/"}, "import-details": {"title": "Import Details", "column-titles": {"file-name": "filename", "type": "Type", "import-speed": "Import Speed", "import-progress": "Import Progress", "status": "status", "time-consumed": "time consumed", "manipulations": "operations"}, "content": {"vertex": "vertex", "edge": "EDGE"}, "status": {"RUNNING": "running", "SUCCEED": "succeeded", "FAILED": "failed", "PAUSED": "paused", "STOPPED": "stopped"}, "manipulations": {"pause": "pause", "resume": "resume", "retry": "Retry", "abort": "abort", "failed-cause": "check rea<PERSON>on"}}, "hint": {"check-vertex": "enabling check is Will Affect Import Performance, Enable As Needed", "no-data": "requesting import", "confirm-navigation": "Confirm navigation to task list?"}, "validator": {"no-empty": "this fire cannot be empty", "need-integer-with-negative": "Please Enter -1 or Dark Integer Greater than 0", "need-integer": "Please Enter press Integer Greater than 0"}, "manipulations": {"previous": "previous", "start": "Start Import", "cancel": "Cancel Import", "finished": "Finished"}}, "data-import-status": {"finished": "Import Completed", "success": "SuccessFully Importing {{number}} Files", "pause": "{{number}} Files Importing Pause", "abort": "{{number}} Files Importing Abort", "move-to-import-manager": "back to import manager"}}