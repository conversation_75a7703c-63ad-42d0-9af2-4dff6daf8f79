{"name": "hubble", "version": "1.6.0", "author": "wang<PERSON><PERSON>", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/hugegraph/hugegraph-hubble"}, "dependencies": {"@types/classnames": "^2.2.10", "@types/codemirror": "^0.0.96", "@types/d3": "^5.7.2", "@types/file-saver": "^2.0.1", "@types/jest": "24.0.15", "@types/lodash-es": "^4.17.3", "@types/node": "14.11.8", "@types/react": "16.9.52", "@types/react-dom": "16.9.8", "@types/react-highlight-words": "^0.16.1", "@types/uuid": "^8.3.0", "@types/validator": "^13.1.0", "antd": "^4.18.5", "axios": "^0.19.0", "classnames": "^2.2.6", "codemirror": "^5.55.0", "file-saver": "^2.0.2", "framer-motion": "^2.1.2", "i18next": "^19.5.3", "less": "^3.11.3", "lodash-es": "^4.17.15", "mobx": "^5.13.0", "mobx-react": "^6.2.2", "prettier": "^2.0.5", "react": "^16.13.1", "react-dnd": "^11.1.3", "react-dnd-html5-backend": "^11.1.3", "react-dom": "^16.13.1", "react-highlight-words": "^0.16.0", "react-i18next": "^11.7.3", "react-json-view": "^1.19.1", "react-popper-tooltip": "^2.11.1", "react-scripts": "3.4.1", "typescript": "^3.9.6", "uuid": "^8.3.1", "validator": "^13.1.1", "vis-network": "7.3.5", "wouter": "^2.5.1"}, "scripts": {"start": "react-app-rewired start", "build": "CI=false && react-app-rewired build && yarn run license", "test": "react-app-rewired test", "license": "node add-license.js"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"customize-cra": "^0.9.1", "less-loader": "^5.0.0", "react-app-rewired": "^2.1.6", "stylelint": "^13.6.1", "stylelint-config-standard": "^20.0.0"}, "keywords": ["hugegraph"]}