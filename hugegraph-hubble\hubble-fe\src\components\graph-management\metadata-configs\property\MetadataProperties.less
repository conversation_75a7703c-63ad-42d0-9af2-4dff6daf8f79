/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with this
 * work for additional information regarding copyright ownership. The ASF
 * licenses this file to You under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

.metadata-configs {
  .metadata-properties {
    &-selected-reveals {
      width: 100%;
      height: 40px;
      padding: 0 16px;
      display: flex;
      background: #2b65ff;
      border-radius: 3px 3px 0 0;
      font-size: 14px;
      align-items: center;
      color: #fff;

      & > div {
        margin-right: 12px;
      }

      & > img {
        margin-left: auto;
        cursor: pointer;
      }
    }

    &-manipulation {
      font-size: 14px;
      color: #2b65ff;
      cursor: pointer;

      &:hover {
        color: #527dff;
      }

      &:active {
        color: #184bcc;
      }
    }

    &-search-highlights {
      background: transparent;
      color: #2b65ff;
      cursor: pointer;
    }
  }
}

.metadata-properties-modal {
  &-title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    & > img {
      cursor: pointer;
    }
  }

  &-description {
    margin-bottom: 16px;
    color: #333;
  }
}

.property-status-not-used {
  width: 58px;
  height: 28px;
  background: #f2fff4;
  border: 1px solid #7ed988;
  border-radius: 2px;
  font-size: 14px;
  color: #39bf45;
  letter-spacing: 0;
  line-height: 28px;
  text-align: center;
}

.property-status-is-using {
  width: 58px;
  height: 28px;
  background: #fff2f2;
  border: 1px solid #ff9499;
  border-radius: 2px;
  font-size: 14px;
  color: #e64552;
  letter-spacing: 0;
  line-height: 28px;
  text-align: center;
}
