/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with this
 * work for additional information regarding copyright ownership. The ASF
 * licenses this file to You under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

.vertex-index-wrapper {
  width: 100%;
  background: #fff;
  border-radius: 0 3px 3px 3px;
  padding: 16px;
}

.vertex-index-tab-wrapper {
  display: flex;
  width: 100%;
  margin-top: 16px;

  & > .vertex-index-tab-index {
    padding: 10px 25px;
    height: 40px;
    color: #333;
    font-size: 14px;
    text-align: center;
    line-height: 20px;
    cursor: pointer;

    &.active {
      background-color: #fff;
      color: #2b65ff;
      border-radius: 3px 3px 0 0;
    }
  }
}

.vertex-index-search-highlights {
  background: transparent;
  color: #2b65ff;
}
