/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with this
 * work for additional information regarding copyright ownership. The ASF
 * licenses this file to You under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

@import '~hubble-ui/src/index.less';

@primary-color: #2b65ff;
@primary-hover-color: #527dff;
@primary-active-color: #184bcc;

* {
  box-sizing: border-box;
}

html,
body,
img,
ul,
ol,
li,
table,
tr,
th,
td,
p {
  margin: 0;
  padding: 0;
  border: 0;
}

body {
  background: #f2f2f2;
  // prettier-ignore
  font-family:
    -apple-system,
    BlinkMacSystemFont,
    'Helvetica Neue',
    Helvetica,
    'PingFang SC',
    'Hiragino Sans GB',
    'Microsoft YaHei',
    '微软雅黑',
    Arial,
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &.dark {
    background: #000;
  }

  &::after {
    position: absolute;
    overflow: hidden;
    left: -20000px;
    // preload images which in hover state to solve blink issue
    content:
      url(./assets/imgs/ic_topback.svg)
      url(./assets/imgs/ic_arrow_white.svg)
      url(./assets/imgs/ic_shuju_normal.svg)
      url(./assets/imgs/ic_shuju_pressed.svg)
      url(./assets/imgs/ic_yuanshuju_normal.svg)
      url(./assets/imgs/ic_yuanshuju_pressed.svg)
      url(./assets/imgs/ic_cebianzhankai.svg)
      url(./assets/imgs/ic_cebianshouqi.svg)
      url(./assets/imgs/ic_daorushuju_normal.svg)
      url(./assets/imgs/ic_daorushuju_pressed.svg)
      url(./assets/imgs/ic_renwuguanli_normal.svg)
      url(./assets/imgs/ic_renwuguanli_pressed.svg)
      url(./assets/imgs/ic_tuzhanshi_normal.svg)
      url(./assets/imgs/ic_tuzhanshi_hover.svg)
      url(./assets/imgs/ic_tuzhanshi_pressed.svg)
      url(./assets/imgs/ic_biaoge_normal.svg)
      url(./assets/imgs/ic_biaoge_hover.svg)
      url(./assets/imgs/ic_biaoge_pressed.svg)
      url(./assets/imgs/ic_json_normal.svg) url(./assets/imgs/ic_json_hover.svg)
      url(./assets/imgs/ic_json_pressed.svg) url(./assets/imgs/ic_close_16.svg)
      url(./assets/imgs/ic_refresh.svg) url(./assets/imgs/ic_loading_back.svg)
      url(./assets/imgs/ic_loading_front.svg)
      url(./assets/imgs/ic_question_mark.svg);
  }
}

code {
  // prettier-ignore
  font-family:
    source-code-pro,
    Menlo,
    Monaco,
    Consolas,
    'Courier New',
    monospace;
}

.tooltips {
  background: #fff;
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 28px 24px 24px;
}

.tooltips-dark {
  border-radius: 3px;
  background-color: rgba(0, 0, 0, 0.8);
  border-color: transparent;
  padding: 4px 8px;
  color: #fff;
  line-height: 18px;
  font-size: 12px;
  max-width: 240px;
  word-wrap: break-word;

  // override tooltip-arrow
  .tooltip-arrow[data-placement*='bottom'] {
    margin-top: -0.25rem;
  }

  .tooltip-arrow[data-placement*='bottom']::before {
    border-color: transparent transparent rgba(0, 0, 0, 0.8) transparent;
    border-width: 0 0.3rem 0.3rem 0.3rem;
    position: absolute;
    top: 0;
  }

  .tooltip-arrow[data-placement*='bottom']::after {
    border-color: transparent transparent rgba(0, 0, 0, 0.8) transparent;
    // border-color: rgba(0, 0, 0, 0.8);
    border-width: 0 0.3rem 0.3rem 0.3rem;
  }
}

.no-line-break {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-wrapper {
  font-size: 14px;
  line-height: 22px;

  &-title {
    font-weight: 900;
    margin: 3.6px 0 4px;
  }

  &-manipulation {
    cursor: pointer;
    color: #2b65ff;
  }
}

/* overrides (global) */

//primary button background colors
.new-fc-one-btn-primary {
  background: @primary-color;

  &:hover {
    background: @primary-hover-color;
  }

  &:active {
    background: @primary-active-color;
  }

  &-disabled {
    background: #d9e6ff;

    &:hover,
    &:active {
      background: #d9e6ff;
    }
  }
}

// Modal titles
.new-fc-one-modal-title {
  // prettier-ignore
  font-family:
    'PingFangSC-Medium',
    'Microsoft YaHei',
    '微软雅黑',
    Arial,
    sans-serif;
  font-weight: bold;
  font-size: 16px;
}

// drawers, e.g. metadata-configs vertex/edge types
.new-fc-one-drawer-wrapper-body-small .new-fc-one-drawer-title {
  font-size: 16px;
}

.new-fc-one-drawer-close {
  top: 28px;
}

// z-index input errorlayer in Drawer should be higher
// prettier-ignore
.new-fc-one-drawer-content-wrapper .new-fc-one-input-error.new-fc-one-input-error-layer {
  z-index: 1041;
}

// also z-index is invalid if parent node has position: relative
.new-fc-one-drawer-content-wrapper .new-fc-one-input-all-container {
  position: absolute;
}

// Steps process style
.new-fc-one-steps-item-process .new-fc-one-steps-item-icon {
  background: @primary-color;
}

// <Swtich /> checked-color
.new-fc-one-switch-checked {
  background: @primary-color;

  &:hover {
    background: @primary-hover-color;
  }
}

// text in <Switch />
.new-fc-one-switch-inner {
  margin-right: 0;
}

.new-fc-one-switch-checked .new-fc-one-switch-inner {
  margin-left: 0;
}

// override radio button primary color
.new-fc-one-radio-button-wrapper-checked {
  border: 1px solid @primary-color;
  background-color: @primary-color;
  color: #fff;

  &:hover {
    border: 1px solid @primary-hover-color;
    background-color: @primary-hover-color;
    color: #fff;
  }
}

// <Tab /> hovered color
.new-fc-one-menu-horizontal-box .new-fc-one-menu-item-selected:hover {
  color: @primary-hover-color;
  border-color: @primary-hover-color;
}

// <Select /> mutliselect icon issue solution
.new-fc-one-select-selection__choice__remove svg.icon-close {
  width: 8px;
  height: 8px;
}

.new-fc-one-checkbox-wrapper .new-fc-one-checkbox-checked .new-fc-one-checkbox-inner {
  background-color: @primary-color;

  &::after {
    background-color: @primary-color;
  }
}

// Radio group style
.new-fc-one-radio-checked .new-fc-one-radio-inner::after {
  background-color: #2b65ff;
}

.new-fc-one-dropdown.improve-zindex {
  z-index: 9999;
}

// <Table /> th checkbox color
.new-fc-one-checkbox-wrapper .new-fc-one-checkbox-indeterminate .new-fc-one-checkbox-inner {
  background-color: #2b65ff;
}

.new-fc-one-table-thead > tr > th:first-of-type {
  padding: 12px 16px 12px 20px;
}

// <Message /> could be a bug where container style is display: none
.new-fc-one-message {
  display: block !important;
}

// <Message /> proper line break
.new-fc-one-message-container-content {
  word-break: break-word;
}

// <Breadcrumb />
.new-fc-one-breadcrumb-link {
  color: #2b65ff;
}

// <Modal />
.new-fc-one-modal-body {
  overflow-y: auto !important;
}

// menu center
.data-analyze-sidebar .ant-menu.ant-menu-inline-collapsed > .ant-menu-item {
  padding: 0 12px;
}
