/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with this
 * work for additional information regarding copyright ownership. The ASF
 * licenses this file to You under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

.job-error-logs {
  width: 75vw;
  position: relative;
  top: 76px;
  margin: 0 auto;
  font-size: 14px;

  &-title {
    margin-bottom: 16px;
    color: #333;
    line-height: 22px;
    font-weight: 900;
  }

  &-content-wrapper {
    display: flex;
    height: calc(100vh - 139px);
    padding-top: 25px;
    padding-right: 16px;
    background-color: #fff;
  }

  &-content {
    margin-left: 19px;
    overflow: auto;
    line-height: 2;

    &-item {
      display: flex;

      &:nth-child(even) {
        margin-bottom: 32px;
      }
    }

    &-item-title {
      word-break: keep-all;
    }

    &-item-text {
      margin-left: 6px;
    }

    &-with-error-only {
      margin-left: 16px;
    }
  }
}
