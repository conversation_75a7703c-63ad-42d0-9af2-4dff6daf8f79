/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with this
 * work for additional information regarding copyright ownership. The ASF
 * licenses this file to You under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

.reuse-properties-wrapper {
  width: 100%;
  background: #fff;
  margin-top: 16px;
  padding: 16px;

  .reuse-steps {
    width: 860px;
    margin: 16px auto 18px;
  }

  .reuse-properties {
    &-row {
      display: flex;
      align-items: center;
      margin: 18px 0 32px;
      font-size: 14px;
      color: #333;

      &:last-child {
        margin-bottom: 24px;
      }

      &-name {
        width: 98px;
        text-align: right;
        margin-right: 13.9px;
      }
    }

    &-manipulations {
      display: flex;
      justify-content: center;
      margin-bottom: 88px;
    }

    &-validate {
      &-duplicate,
      &-exist,
      &-pass {
        width: 58px;
        margin: 0 auto;
        border-radius: 2px;
        letter-spacing: 0;
        line-height: 22px;
        text-align: center;
      }

      &-duplicate {
        background: #fff2f2;
        border: 1px solid #ff9499;
        color: #e64552;
      }

      &-exist,
      &-pass {
        background: #f2fff4;
        border: 1px solid #7ed988;
        color: #39bf45;
      }
    }

    &-complete-hint {
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin: 88px auto 327px;

      &-description {
        display: flex;
        justify-content: center;

        & > div {
          margin-left: 20px;
        }
      }

      &-manipulations {
        margin: 42px auto 0;
      }
    }
  }
}
