/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with this
 * work for additional information regarding copyright ownership. The ASF
 * licenses this file to You under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

.metadata-graph {
  &-drawer-wrapper {
    .metadata-graph-drawer {
      &-title {
        font-family:
          'PingFangSC-Medium',
          'Microsoft YaHei Bold',
          '微软雅黑',
          Arial,
          sans-serif;
        width: 115px;
        font-weight: bold;
        margin-bottom: 16px;
        text-align: right;
        word-break: keep-all;
      }

      &-manipulations,
      &-options {
        margin-bottom: 32px;
        display: flex;
        align-items: center;

        &-name {
          width: 125px;
          margin-right: 43.9px;
          text-align: right;
          color: #333;
          font-size: 14px;
          word-break: keep-all;
          line-height: 32px;
        }

        &-expands {
          font-size: 14px;
          color: #333;
        }
      }
    }
  }

  &-view-wrapper {
    height: calc(100vh - 222px);
    overflow: hidden;
  }

  &-view {
    margin: -1px;
  }

  &-view-empty-wrapper {
    height: calc(100vh - 222px);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &-view-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    font-size: 14px;
    color: #333;
  }

  &-loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
  }

  &-loading-bg {
    width: 144px;
    height: 144px;
    position: relative;
    margin-bottom: 10px;
  }

  &-loading-back {
    position: absolute;
    left: 22px;
    top: 20px;
  }

  &-loading-front {
    position: absolute;
    left: 57.1px;
    top: 52.1px;
    animation: loading-rotate 2s linear infinite;
  }

  @keyframes loading-rotate {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  &-property-manipulation {
    font-size: 14px;
    color: #2b65ff;
    cursor: pointer;

    &:hover {
      color: #527dff;
    }

    &:active {
      color: #184bcc;
    }
  }

  &-tooltips {
    background: #fff;
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.15);
    border-radius: 3px;
    padding: 16px;
    font-size: 14px;
    color: #333;

    &-title {
      font-size: 16px;
      font-weight: 900;
      margin-bottom: 16px;
    }
  }
}

/* override */

// refer to index.less in root dir, this should be fixed in future
.metadata-graph-drawer-wrapper {
  .new-fc-one-input-all-container {
    position: relative;
  }
}

.metadata-graph-drawer-options-colors {
  margin-left: 12px;
}

.metadata-graph-drawer-options-color {
  width: 20px;
  height: 20px;
}

.metadata-graph-drawer-options .metadata-graph-drawer-options-color {
  margin-top: 6px;
}

// refer to NewVertexType.less, color option style in dropdown
.new-fc-one-select-dropdown-menu-container .metadata-graph-drawer-options-color {
  width: 20px;
  height: 20px;
  margin: 6px auto;
}
