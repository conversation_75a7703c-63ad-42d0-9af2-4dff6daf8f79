/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with this
 * work for additional information regarding copyright ownership. The ASF
 * licenses this file to You under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations
 * under the License.
 */

.import-job-details {
  position: absolute;
  width: calc(100% - 60px);
  padding: 0 16px 16px;
  left: 60px;
  top: 60px;

  &-with-expand-sidebar {
    width: calc(100% - 200px);
    left: 200px;
  }

  &-breadcrumb-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
  }

  &-content-wrapper {
    height: calc(100vh - 201px);
    background: #fff;
    padding: 24px;
    overflow: auto;
  }

  &-content-header {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
  }

  &-basic-text {
    font-size: 14px;
    color: #333;

    & > div {
      margin-bottom: 14px;
    }

    & span:last-child {
      margin-left: 24px;
    }
  }

  &-uploaded-file-infos {
    margin-bottom: 19px;
    font-size: 14px;

    &-titles {
      width: 300px;
      display: flex;
      justify-content: space-between;
    }

    &-progress-status {
      width: 374px;
      display: flex;
      align-items: center;
    }
  }
}

/* override */

// reveal error layer in <Modal />
.new-fc-one-modal-body {
  overflow: visible;
}
