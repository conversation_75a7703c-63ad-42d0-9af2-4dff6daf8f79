{"addition": {"function-parameter": {"edge-type": "Edge Type", "vertex-id": "Vertex ID"}, "store": {"required": "Required", "item-is-required": "This item is required", "no-match-input-requirements": "Does not match input requirements", "rule1": "Please enter letters, numbers, or special characters", "rule2": "Please enter a number in the range of 1-65535", "rule3": "Username and password must be filled in at the same time", "rule4": "Must be in Chinese, English, numbers, and underscores", "illegal-data-format": "Illegal data format", "incorrect-time-format": "Incorrect time format", "cannot-be-empty": "This item cannot be empty", "cannot-be-empty1": "This item cannot be empty", "same-edge-name-notice": "Same edge name exists, please enter another name", "same-vertex-name-notice": "Same vertex name exists, please enter another name", "same-property-name-notice": "Same property name exists, please enter another name", "same-index-name-notice": "Same property index name exists, please enter another name", "network-error": "Network anomaly, please try again later"}, "constant": {"primary-key-id": "Primary Key ID", "automatic-generation": "Automatic Generation", "custom-string": "Custom String", "custom-number": "Custom Number", "custom-uuid": "Custom UUID", "greater-than": "Greater Than", "greater-than-or-equal": "Greater Than or Equal", "less-than": "Less Than", "less-than-or-equal": "Less Than or Equal", "equal": "Equal"}, "menu": {"chart": "Chart", "table": "Table", "task-id": "Task ID", "list-mode": "List Mode", "chart-mode": "Chart Mode", "secondary-index": "Secondary Index", "range-index": "Range Index", "full-text-index": "Full Text Index", "type-index": "Type Index", "base-info": "Basic Information", "select-reuse-item": "Select Reuse Item", "confirm-reuse-item": "Confirm Reuse Item", "complete-reuse": "Complete Reuse"}, "vertex": {"type-detail": "Vertex Type Details", "edit-type": "Edit Vertex Type", "using-cannot-delete": "The current vertex type is in use and cannot be deleted.", "using-cannot-delete-confirm": "The vertex type in use cannot be deleted. Are you sure you want to delete the following unused vertex types?", "del-vertex-confirm": "Confirm deletion of this vertex type?", "del-vertex-confirm-again": "Confirm deletion of this vertex type? Deletion is irreversible, please proceed with caution", "vertex-type-name": "Vertex Type Name", "vertex-style": "Vertex Style", "vertex-display-content": "Vertex Display Content", "select-vertex-display-content-placeholder": "Please select vertex display content", "create-vertex-type": "Create Vertex Type", "vertex-index": "Vertex Index", "corresponding-vertex-type": "Corresponding Vertex Type", "no-vertex-type-desc": "You currently do not have any vertex types, create one now"}, "edge": {"display-content": "Edge Display Content", "display-content-select-desc": "Please select edge display content", "index-info": "Index Information", "index-name": "Index Name", "index-type": "Index Type", "edge-index": "Edge Index", "index-type-select-desc": "Please select index type", "property-select-desc": "Please select property", "add-group": "Add a group", "confirm-del-edge-type": "Confirm deletion of this edge type?", "confirm-del-edge-type-again": "Confirm deletion of edge type? Deletion is irreversible, please proceed with caution", "confirm-del-edge-careful-notice": "Deletion is irreversible, please proceed with caution", "no-edge-desc": "You currently do not have any edge types, create one now", "create-edge": "Create Edge Type", "multiplexing-existing-type": "Multiplex Existing Type", "multiplexing-edge-type": "Multiplex Edge Type", "multiplexing-edge-type-notice": "The attributes and attribute indexes, start and end types, and associated attributes and attribute indexes of the edge type will be reused together", "verification-result": "Verification Result", "be-verified": "To be verified", "verified-again": "Re-verify"}, "message": {"no-can-delete-vertex-type": "No deletable vertex types", "no-index-notice": "You currently do not have any indexes", "property-create-desc": "You currently do not have any properties, create one now", "del-unused-property-notice": "In-use properties cannot be deleted. Confirm deletion of the following unused properties?", "please-enter-keywords": "Please enter search keywords", "no-property-can-delete": "No deletable properties", "no-metadata-notice": "You currently do not have any metadata", "no-vertex-or-edge-notice": "You have not set any vertex types or edge types", "no-adjacency-points": "No adjacent points", "no-more-points": "No more adjacent points", "please-enter-number": "Please enter a number", "please-enter-string": "Please enter a string", "please-enter": "Please enter", "no-chart-desc": "No graph results, please view the table or JSON data", "no-data-desc": "No data results at the moment", "data-loading": "Data loading", "submit-async-task": "Submitting asynchronous task", "submit-success": "Submission successful", "submit-fail": "Submission failed", "task-submit-fail": "Task submission failed", "fail-reason": "Failure reason", "fail-position": "Failure position", "selected": "Selected", "edge-del-confirm": "Confirm deletion of the following edges?", "long-time-notice": "Deleting metadata may take a long time, details can be viewed in task management", "index-long-time-notice": "Creating indexes may take a long time, details can be viewed in task management", "property-del-confirm": "Confirm deletion of this property?", "property-del-confirm-again": "Confirm deletion of this property? Deletion is irreversible, please proceed with caution", "index-del-confirm": "After deleting the index, queries based on this property index will not be possible. Proceed with caution.", "edge-name-rule": "Allowing Chinese, English, numbers, and underscores", "source-type-select-placeholder": "Please select source type", "target-type-select-placeholder": "Please select target type", "select-distinguishing-key-property-placeholder": "Please select distinguishing key property", "select-association-key-property-placeholder": "Please select associated property", "index-open-notice": "Enabling indexes may affect performance, enable as needed", "duplicate-name": "Duplicate name", "dependency-conflict": "Dependency conflict", "already-exist": "Already exists", "pass": "Pass", "select-reuse-graph-placeholder": "Please select the graph to reuse", "reuse-complete": "Reuse complete", "vertex-type-reuse-success": "Vertex type reuse successful", "property-using-cannot-delete": "The current property data is in use and cannot be deleted.", "reuse-property-success": "Property reuse successful", "reuse-vertex-type-notice": "The attributes and attribute indexes associated with the vertex type will be reused together", "illegal-vertex": "This vertex is an illegal vertex, possibly caused by a dangling edge"}, "operate": {"reuse-vertex-type": "Reuse Vertex Type", "reuse-existing-property": "Reuse Existing Property", "reuse-property": "Reuse Property", "create-property": "Create Property", "continue-reuse": "Continue Reuse", "back-to-view": "Back to View", "complete": "Complete", "next-step": "Next Step", "previous-step": "Previous Step", "rename": "<PERSON><PERSON>", "del-ing": "Deleting", "view-task-management": "View Task Management", "batch-del": "<PERSON><PERSON> Delete", "multiplexing": "Multiplex", "de-multiplexing": "Cancel Multiplexing", "open": "Open", "close": "Close", "look": "View", "view-property": "View Property", "filter": "Filter", "add-filter-item": "Add Property Filter", "enlarge": "Enlarge", "narrow": "Shrink", "center": "Center", "download": "Download", "exit-full-screen": "Exit Full Screen", "full-screen": "Full Screen", "load-background": "Load <PERSON>", "load-spinner": "<PERSON><PERSON>ner", "rendering": "Rendering", "detail": "Detail", "favorite": "Favorite", "favorite-success": "Favorite successful", "load-statement": "Load Statement", "time": "Time", "name": "Name", "favorite-statement": "Favorite Statement", "favorite-desc": "Please enter favorite name", "operate": "Operate", "query": "Query", "hidden": "<PERSON>de", "modify-name": "Modify Name", "execution-record": "Execution Record", "favorite-queries": "Favorite Queries", "favorite-search-desc": "Search favorite name or statement", "expand-collapse": "Expand/Collapse", "expand": "Expand", "collapse": "Collapse", "favorite-del-desc": "Are you sure you want to delete this favorite statement?", "input-query-statement": "Please input query statement", "query-statement-required": "Query statement cannot be empty", "execute-query": "Execute Query", "execute-task": "Execute Task", "execute-ing": "Executing", "clean": "Clear", "modify-success": "Modification successful", "query-result-desc": "Query mode is suitable for small-scale analysis with results returned within 30 seconds; task mode is suitable for large-scale analysis with longer result return times, task details can be viewed in task management"}, "common": {"in-use": "In Use", "not-used": "Not Used", "status": "Status", "no-result": "No Result", "cardinal-number": "Cardinal Number", "allow-null": "Allow <PERSON>", "allow-multiple-connections": "Allow Multiple Connections", "multiple-connections-notice": "Enabling this allows multiple edges of the same type between two vertices", "term": "Items", "in-edge": "In Edge", "add-in-edge": "Add In Edge", "out-edge": "Out Edge", "add-out-edge": "Add Out Edge", "add": "Add", "value": "Value", "null-value": "Null Value", "id-strategy": "ID Strategy", "id-value": "ID Value", "id-input-desc": "Please enter ID value", "please-input": "Please enter", "add-success": "Add successful", "add-fail": "Add failed", "vertex-type": "Vertex Type", "selected-vertex-type": "Selected Vertex Type", "vertex-id": "Vertex ID", "vertex-name": "Vertex Name", "illegal-vertex-desc": "This vertex is an illegal vertex, possibly caused by a dangling edge", "add-vertex": "Add Vertex", "vertex-type-select-desc": "Please select vertex type", "edge-type": "Edge Type", "selected-edge-type": "Selected Edge Type", "edge-style": "Edge Style", "modify-edge-type": "Edit Edge Type", "edge-type-detail": "Edge Type Details", "edge-type-name": "Edge Type Name", "edge-direction": "Edge Direction", "edge-type-select-desc": "Please select edge type", "edge-id": "Edge ID", "edge-name": "Edge Name", "source": "Source", "source-type": "Source Type", "target": "Target", "target-type": "Target Type", "rule": "Rule", "property": "Property", "selected-property": "Selected Property", "property-name": "Property Name", "add-property": "Add Property", "association-property": "Association Property", "association-property-and-type": "Association Property and Type", "property-index": "Property Index", "selected-property-index": "Selected Property Index", "property-index-name": "Property Index Name", "property-value": "Property Value", "required-property": "Required Property", "nullable-property": "Nullable Property", "primary-key": "Primary Key", "primary-key-property": "Primary Key Property", "select-primary-key-property-placeholder": "Please select primary key property", "distinguishing-key": "Distinguishing Key", "distinguishing-key-property": "Distinguishing Key Property", "property-input-desc": "Please enter property value", "del-comfirm": "Confirm deletion", "ask": "?", "confirm": "Confirm", "del-success": "Deletion successful", "save-scuccess": "Save successful", "save-fail": "Save failed", "save": "Save", "cancel": "Cancel", "more": "More", "edit": "Edit", "del": "Delete", "fold": "Fold", "open": "Expand", "close": "Close", "required": "Required", "format-error-desc": "Must start with a letter, allow English, numbers, underscore", "no-matching-results": "No matching results", "no-data": "No data", "data-type": "Data Type", "corresponding-type": "Corresponding Type"}, "appbar": {"graph-manager": "Graph Manager"}, "graphManagementHeader": {"graph-manager": "Graph Manager", "community": "Community Edition", "business": "Business Edition", "limit-desc": "Graph Limit Support", "limit-desc1": "Graph Disk Limit", "individual": "Individual", "input-placeholder": "Search for graph name or ID", "graph-create": "Create Graph"}, "graphManagementList": {"save-scuccess": "Save successful", "graph-del": "Delete Graph", "graph-del-comfirm-msg": "Once deleted, all configurations for this graph cannot be restored", "graph-edit": "Edit G<PERSON>", "id": "Graph ID", "id-desc": "Set a unique identifier for the created graph", "name": "Graph Name", "name-desc": "Enter the name of the graph to connect to", "host": "Hostname", "port": "Port Number", "port-desc": "Please enter port number", "username": "Username", "password": "Password", "creation-time": "Creation Time", "visit": "Visit"}, "graphManagementEmptyList": {"graph-create": "Create Graph", "graph-create-desc": "You currently do not have any graphs, create one now", "no-matching-results": "No matching results"}, "newGraphConfig": {"graph-create": "Create Graph", "create": "Create", "create-scuccess": "Create successful", "id": "Graph ID", "id-desc": "Set a unique identifier for the created graph", "name": "Graph Name", "name-desc": "Enter the name of the graph to connect to", "host": "Hostname", "host-desc": "Please enter hostname", "port": "Port Number", "port-desc": "Please enter port number", "username": "Username", "password": "Password", "not-required-desc": "Leave blank if not required"}, "graphManagementSidebar": {"data-analysis": "Data Analysis", "graph-select": "Select Graph", "metadata-config": "Metadata Configuration", "data-import": "Data Import", "task-management": "Task Management"}, "dataAnalyze": {"cannot-access": "Cannot Access", "return-home": "Return Home"}, "dataAnalyzeInfoDrawer": {"edit-details": "Edit Details", "data-details": "Data Details"}}}